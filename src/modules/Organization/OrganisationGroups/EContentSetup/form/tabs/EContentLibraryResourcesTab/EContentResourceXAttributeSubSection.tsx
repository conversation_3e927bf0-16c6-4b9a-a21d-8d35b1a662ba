import React, { useEffect, useMemo, useState } from 'react';
import classnames from 'classnames';
import { get, isEmpty } from 'lodash';
import uuidv4 from 'uuid/v4';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import CheckboxField from '../../../../../../../common/components/containers/EntityForm/fields/CheckboxField';
import NumberField from '../../../../../../../common/components/containers/EntityForm/fields/NumberField';
import RuleField from '../../../../../../../common/components/containers/EntityForm/fields/RuleField';
import { MANDATORY } from '../../../../../../../model/RuleType';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import {
  CHARACTERS_DEFAULT,
  MAXIMUM_CHARACTERS_DEFAULT,
} from './EContentResourceForm';
import { useDependsOnFields } from '../../../../../../../common/components/containers/EntityForm';
import TextField from '../../../../../../../common/components/controls/base/TextField';
import IconButton from '../../../../../../../common/components/controls/IconButton';
import styles from './EContentResourceXAttributeSubSection.scss';
import useEntityFormContext from '../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';

export interface IEContentResourceXAttributeSubSection {
  name: string;
  label: string;
  hasMaximumCharactersFields?: boolean;
  hasCategoryOptions?: boolean;
  hasRuleField?: boolean;
  columns?: number;
  prefix?: string;
}
const EContentResourceXAttributeSubSection: React.FC<IEContentResourceXAttributeSubSection> = ({
  name,
  label,
  hasMaximumCharactersFields = false,
  hasCategoryOptions = false,
  hasRuleField = true,
  columns,
  prefix = 'cookedAttributes',
}) => {
  const t = useT();
  const [hoverIndex, setHoverIndex] = useState<number>();

  const { isChecked } = useDependsOnFields({
    isChecked: `${prefix}.${name}.isChecked`,
  });

  const _temp = {
    id: uuidv4(),
    name: '',
  };
  const { values, setFieldValue, setFieldTouched } = useEntityFormContext();
  const categoriesValue = useMemo(
    () => get(values, `${prefix}.${name}.categories`, [_temp]),
    [prefix, name, values, _temp],
  );
  const [categories, setCategories] = useState(
    !isEmpty(categoriesValue) ? categoriesValue : [_temp],
  );

  // Reset categories when checkbox is checked/unchecked to ensure fresh data
  useEffect(() => {
    if (isChecked) {
      // When checkbox is checked, reset categories to current form values
      const currentCategoriesValue = get(
        values,
        `${prefix}.${name}.categories`,
        [_temp],
      );
      setCategories(
        !isEmpty(currentCategoriesValue) ? currentCategoriesValue : [_temp],
      );
    }
  }, [isChecked, values, prefix, name, _temp]);

  useEffect(() => {
    if (!isEmpty(categories[categories.length - 1]?.name)) {
      setCategories([...categories, _temp]);
    }
  }, [categories, _temp]);

  return (
    <>
      <EntityFormFieldSet>
        <CheckboxField
          columns={1}
          label={label}
          name={`${prefix}.${name}.isChecked`}
        />
      </EntityFormFieldSet>
      {isChecked ? (
        <EntityFormFieldSet className="pl-20">
          {hasCategoryOptions
            ? categories.map((cat, i) => {
                const handleRemoveField = () => {
                  if (categories.length > 1) {
                    const _cat = [...categories];
                    _cat.splice(i, 1);
                    setCategories(_cat);
                    setFieldValue(`${prefix}.${name}.categories`, _cat);
                    setFieldTouched(`${prefix}.${name}.categories`, true);
                  }
                };
                const onChange = e => {
                  const _cat = [...categories];
                  _cat[i].name = e.target.value;
                  setCategories(_cat);
                  setFieldValue(`${prefix}.${name}.categories`, _cat);
                  setFieldTouched(`${prefix}.${name}.categories`, true);
                };
                const handleMouseEnter = () => {
                  setHoverIndex(i);
                };
                const handleMouseLeave = () => {
                  setHoverIndex(undefined);
                };
                return (
                  <div
                    // eslint-disable-next-line react/no-array-index-key
                    key={cat.id}
                    className={styles.flex}
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                  >
                    <TextField
                      className="col-lg-4"
                      maxLength={70}
                      placeholder={t('Enter Category')}
                      value={cat.name}
                      onChange={onChange}
                    />
                    {hoverIndex === i && (
                      <IconButton
                        hasBackground={false}
                        iconClassName={classnames(styles.removeIcon)}
                        iconName="cross2"
                        title={t('Remove')}
                        onClick={handleRemoveField}
                      />
                    )}
                  </div>
                );
              })
            : null}
          {hasMaximumCharactersFields ? (
            <NumberField
              required
              columns={columns}
              defaultValue={CHARACTERS_DEFAULT}
              label={t('Maximum Characters of')}
              max={MAXIMUM_CHARACTERS_DEFAULT}
              name={`${prefix}.${name}.value`}
            />
          ) : null}
          {hasRuleField ? (
            <RuleField
              isRequired
              columns={columns}
              defaultValue={MANDATORY.value}
              name={`${prefix}.${name}.rule`}
            />
          ) : null}
        </EntityFormFieldSet>
      ) : null}
    </>
  );
};

export default EContentResourceXAttributeSubSection;
